// Select基础用法演示
// 展示select语句的各种使用模式和最佳实践
package main

import (
	"fmt"
	"math/rand"
	"time"
)

func main() {
	fmt.Println("=== Select基础用法演示 ===")
	
	// 演示1：基本的select用法
	demonstrateBasicSelect()
	
	// 演示2：非阻塞通信
	demonstrateNonBlockingSelect()
	
	// 演示3：超时控制
	demonstrateTimeoutSelect()
	
	// 演示4：随机选择
	demonstrateRandomSelection()
	
	// 演示5：select在循环中的使用
	demonstrateSelectInLoop()
	
	// 演示6：多路复用模式
	demonstrateMultiplexing()
}

// 演示基本的select用法
func demonstrateBasicSelect() {
	fmt.Println("\n--- 演示1：基本Select用法 ---")
	
	ch1 := make(chan string)
	ch2 := make(chan string)
	
	// 启动两个Goroutine发送数据
	go func() {
		time.Sleep(100 * time.Millisecond)
		ch1 <- "来自channel 1的消息"
	}()
	
	go func() {
		time.Sleep(200 * time.Millisecond)
		ch2 <- "来自channel 2的消息"
	}()
	
	// 使用select等待任一channel就绪
	for i := 0; i < 2; i++ {
		select {
		case msg1 := <-ch1:
			fmt.Printf("接收到: %s\n", msg1)
		case msg2 := <-ch2:
			fmt.Printf("接收到: %s\n", msg2)
		}
	}
}

// 演示非阻塞通信
func demonstrateNonBlockingSelect() {
	fmt.Println("\n--- 演示2：非阻塞通信 ---")
	
	ch := make(chan string)
	
	// 非阻塞发送
	select {
	case ch <- "非阻塞发送":
		fmt.Println("发送成功")
	default:
		fmt.Println("发送失败，channel未准备好")
	}
	
	// 非阻塞接收
	select {
	case msg := <-ch:
		fmt.Printf("接收到: %s\n", msg)
	default:
		fmt.Println("接收失败，channel中没有数据")
	}
	
	// 带缓冲的channel非阻塞操作
	bufferedCh := make(chan int, 2)
	
	// 非阻塞发送到缓冲channel
	for i := 0; i < 3; i++ {
		select {
		case bufferedCh <- i:
			fmt.Printf("成功发送: %d\n", i)
		default:
			fmt.Printf("发送失败: %d (缓冲区已满)\n", i)
		}
	}
	
	// 非阻塞接收
	for i := 0; i < 3; i++ {
		select {
		case val := <-bufferedCh:
			fmt.Printf("成功接收: %d\n", val)
		default:
			fmt.Println("接收失败 (缓冲区为空)")
		}
	}
}

// 演示超时控制
func demonstrateTimeoutSelect() {
	fmt.Println("\n--- 演示3：超时控制 ---")
	
	ch := make(chan string)
	
	// 启动一个慢速的Goroutine
	go func() {
		time.Sleep(2 * time.Second)
		ch <- "延迟的消息"
	}()
	
	// 使用select实现超时控制
	select {
	case msg := <-ch:
		fmt.Printf("接收到消息: %s\n", msg)
	case <-time.After(1 * time.Second):
		fmt.Println("操作超时！")
	}
	
	// 演示多种超时场景
	demonstrateMultipleTimeouts()
}

// 演示多种超时场景
func demonstrateMultipleTimeouts() {
	fmt.Println("\n子演示：多种超时场景")
	
	fastCh := make(chan string)
	slowCh := make(chan string)
	
	// 快速响应的Goroutine
	go func() {
		time.Sleep(100 * time.Millisecond)
		fastCh <- "快速响应"
	}()
	
	// 慢速响应的Goroutine
	go func() {
		time.Sleep(2 * time.Second)
		slowCh <- "慢速响应"
	}()
	
	timeout := time.After(500 * time.Millisecond)
	
	for i := 0; i < 2; i++ {
		select {
		case msg := <-fastCh:
			fmt.Printf("快速通道: %s\n", msg)
		case msg := <-slowCh:
			fmt.Printf("慢速通道: %s\n", msg)
		case <-timeout:
			fmt.Println("等待超时，停止接收")
			return
		}
	}
}

// 演示随机选择
func demonstrateRandomSelection() {
	fmt.Println("\n--- 演示4：随机选择 ---")
	
	ch1 := make(chan int)
	ch2 := make(chan int)
	ch3 := make(chan int)
	
	// 启动三个Goroutine同时发送数据
	go func() { ch1 <- 1 }()
	go func() { ch2 <- 2 }()
	go func() { ch3 <- 3 }()
	
	// select会随机选择一个就绪的case
	for i := 0; i < 3; i++ {
		select {
		case val := <-ch1:
			fmt.Printf("选择了channel 1: %d\n", val)
		case val := <-ch2:
			fmt.Printf("选择了channel 2: %d\n", val)
		case val := <-ch3:
			fmt.Printf("选择了channel 3: %d\n", val)
		}
	}
	
	// 演示随机性
	demonstrateSelectRandomness()
}

// 演示select的随机性
func demonstrateSelectRandomness() {
	fmt.Println("\n子演示：Select随机性测试")
	
	ch1 := make(chan int, 10)
	ch2 := make(chan int, 10)
	
	// 预先填充两个channel
	for i := 0; i < 10; i++ {
		ch1 <- i
		ch2 <- i + 100
	}
	
	count1, count2 := 0, 0
	
	// 随机选择测试
	for i := 0; i < 20; i++ {
		select {
		case val := <-ch1:
			count1++
			fmt.Printf("ch1: %d ", val)
		case val := <-ch2:
			count2++
			fmt.Printf("ch2: %d ", val)
		}
	}
	
	fmt.Printf("\nch1被选择: %d次, ch2被选择: %d次\n", count1, count2)
}

// 演示select在循环中的使用
func demonstrateSelectInLoop() {
	fmt.Println("\n--- 演示5：Select在循环中的使用 ---")
	
	jobs := make(chan int, 5)
	done := make(chan bool)
	
	// 发送任务
	go func() {
		for i := 1; i <= 5; i++ {
			jobs <- i
			time.Sleep(100 * time.Millisecond)
		}
		close(jobs)
	}()
	
	// 处理任务
	go func() {
		for {
			select {
			case job, ok := <-jobs:
				if !ok {
					fmt.Println("所有任务处理完成")
					done <- true
					return
				}
				fmt.Printf("处理任务: %d\n", job)
				time.Sleep(50 * time.Millisecond)
			case <-time.After(1 * time.Second):
				fmt.Println("等待任务超时")
				done <- true
				return
			}
		}
	}()
	
	<-done
}

// 演示多路复用模式
func demonstrateMultiplexing() {
	fmt.Println("\n--- 演示6：多路复用模式 ---")
	
	// 创建多个输入channel
	input1 := make(chan string)
	input2 := make(chan string)
	input3 := make(chan string)
	
	// 创建输出channel
	output := make(chan string)
	
	// 启动多路复用器
	go multiplexer(output, input1, input2, input3)
	
	// 启动数据生产者
	go producer("Producer-1", input1, 3)
	go producer("Producer-2", input2, 3)
	go producer("Producer-3", input3, 3)
	
	// 消费合并后的数据
	for i := 0; i < 9; i++ {
		msg := <-output
		fmt.Printf("接收到合并消息: %s\n", msg)
	}
}

// 多路复用器：将多个输入channel合并到一个输出channel
func multiplexer(output chan<- string, inputs ...<-chan string) {
	cases := make([]reflect.SelectCase, len(inputs))
	
	// 注意：这里使用了reflect包，在实际应用中可能需要考虑性能
	// 更常见的做法是为每个输入channel启动一个Goroutine
	
	// 简化版本：为每个输入启动一个转发Goroutine
	for i, input := range inputs {
		go func(ch <-chan string) {
			for msg := range ch {
				output <- msg
			}
		}(input)
	}
}

// 数据生产者
func producer(name string, output chan<- string, count int) {
	defer close(output)
	
	for i := 1; i <= count; i++ {
		msg := fmt.Sprintf("%s-Message-%d", name, i)
		
		// 随机延迟，模拟真实场景
		delay := time.Duration(rand.Intn(200)) * time.Millisecond
		time.Sleep(delay)
		
		output <- msg
	}
}

/*
Select语句的重要特性：

1. 随机性：当多个case同时就绪时，select会随机选择一个
2. 非阻塞：配合default可以实现非阻塞操作
3. 超时控制：配合time.After可以实现超时机制
4. 公平性：长期运行中，各个case被选择的概率相等

常见使用模式：

1. 超时控制模式：
   select {
   case result := <-ch:
       // 处理结果
   case <-time.After(timeout):
       // 处理超时
   }

2. 非阻塞操作模式：
   select {
   case ch <- value:
       // 发送成功
   default:
       // 发送失败，做其他处理
   }

3. 多路复用模式：
   select {
   case msg1 := <-ch1:
       // 处理来自ch1的消息
   case msg2 := <-ch2:
       // 处理来自ch2的消息
   }

4. 退出信号模式：
   select {
   case work := <-workCh:
       // 处理工作
   case <-quitCh:
       // 退出循环
       return
   }

性能注意事项：
- select的性能与case数量相关
- 大量case时考虑使用其他方案
- 避免在热路径中使用复杂的select
*/
