// 工作窃取算法演示
// 展示Go调度器如何通过工作窃取实现负载均衡
package main

import (
	"fmt"
	"math/rand"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

// 任务统计信息
type TaskStats struct {
	ProcessorID int
	TaskCount   int64
	TotalTime   time.Duration
}

// 全局统计
var (
	globalTaskCount int64
	processorStats  = make(map[int]*TaskStats)
	statsMutex      sync.RWMutex
)

func main() {
	fmt.Println("=== 工作窃取算法演示 ===")
	
	// 设置处理器数量
	numProcs := runtime.NumCPU()
	runtime.GOMAXPROCS(numProcs)
	
	fmt.Printf("处理器数量: %d\n", numProcs)
	fmt.Printf("CPU核心数: %d\n", runtime.NumCPU())
	
	// 初始化统计信息
	initStats(numProcs)
	
	// 演示1：均匀负载分布
	demonstrateEvenWorkload()
	
	// 演示2：不均匀负载分布
	demonstrateUnevenWorkload()
	
	// 演示3：动态负载变化
	demonstrateDynamicWorkload()
	
	// 演示4：工作窃取效果对比
	compareWithAndWithoutStealing()
}

// 初始化统计信息
func initStats(numProcs int) {
	statsMutex.Lock()
	defer statsMutex.Unlock()
	
	for i := 0; i < numProcs; i++ {
		processorStats[i] = &TaskStats{
			ProcessorID: i,
			TaskCount:   0,
			TotalTime:   0,
		}
	}
	atomic.StoreInt64(&globalTaskCount, 0)
}

// 演示均匀负载分布
func demonstrateEvenWorkload() {
	fmt.Println("\n--- 演示1：均匀负载分布 ---")
	
	resetStats()
	start := time.Now()
	
	var wg sync.WaitGroup
	numTasks := 100
	
	// 创建均匀的任务负载
	for i := 0; i < numTasks; i++ {
		wg.Add(1)
		go func(taskID int) {
			defer wg.Done()
			executeTask(taskID, 10*time.Millisecond, "均匀任务")
		}(i)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	printWorkloadStats("均匀负载", duration)
}

// 演示不均匀负载分布
func demonstrateUnevenWorkload() {
	fmt.Println("\n--- 演示2：不均匀负载分布 ---")
	
	resetStats()
	start := time.Now()
	
	var wg sync.WaitGroup
	
	// 创建不均匀的任务负载
	// 一些长任务和大量短任务
	
	// 长任务（模拟某个P被长任务占用）
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(taskID int) {
			defer wg.Done()
			executeTask(taskID, 100*time.Millisecond, "长任务")
		}(i)
	}
	
	// 短任务（会被其他P窃取）
	for i := 0; i < 95; i++ {
		wg.Add(1)
		go func(taskID int) {
			defer wg.Done()
			executeTask(taskID+100, 5*time.Millisecond, "短任务")
		}(i)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	printWorkloadStats("不均匀负载", duration)
}

// 演示动态负载变化
func demonstrateDynamicWorkload() {
	fmt.Println("\n--- 演示3：动态负载变化 ---")
	
	resetStats()
	start := time.Now()
	
	var wg sync.WaitGroup
	
	// 阶段1：轻负载
	for i := 0; i < 20; i++ {
		wg.Add(1)
		go func(taskID int) {
			defer wg.Done()
			executeTask(taskID, 5*time.Millisecond, "轻负载")
		}(i)
	}
	
	// 等待一段时间
	time.Sleep(50 * time.Millisecond)
	
	// 阶段2：重负载突发
	for i := 0; i < 80; i++ {
		wg.Add(1)
		go func(taskID int) {
			defer wg.Done()
			// 随机任务时长，模拟真实场景
			duration := time.Duration(rand.Intn(20)+5) * time.Millisecond
			executeTask(taskID+1000, duration, "突发任务")
		}(i)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	printWorkloadStats("动态负载", duration)
}

// 对比有无工作窃取的效果
func compareWithAndWithoutStealing() {
	fmt.Println("\n--- 演示4：工作窃取效果对比 ---")
	
	// 注意：Go的调度器总是启用工作窃取，这里只是概念性演示
	// 实际中我们通过控制Goroutine的分布来模拟效果
	
	fmt.Println("模拟无工作窃取场景（任务绑定到特定P）：")
	simulateWithoutStealing()
	
	fmt.Println("\n正常工作窃取场景：")
	simulateWithStealing()
}

// 模拟无工作窃取的场景
func simulateWithoutStealing() {
	resetStats()
	start := time.Now()
	
	var wg sync.WaitGroup
	numProcs := runtime.GOMAXPROCS(0)
	
	// 将任务不均匀地分配给不同的"虚拟P"
	for p := 0; p < numProcs; p++ {
		// 第一个P分配更多任务
		taskCount := 10
		if p == 0 {
			taskCount = 70
		}
		
		for i := 0; i < taskCount; i++ {
			wg.Add(1)
			go func(procID, taskID int) {
				defer wg.Done()
				// 模拟绑定到特定处理器的行为
				executeTaskOnProcessor(procID, taskID, 10*time.Millisecond, "绑定任务")
			}(p, i)
		}
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	printWorkloadStats("无工作窃取模拟", duration)
}

// 模拟正常工作窃取的场景
func simulateWithStealing() {
	resetStats()
	start := time.Now()
	
	var wg sync.WaitGroup
	
	// 创建大量任务，让调度器自由分配
	for i := 0; i < 100; i++ {
		wg.Add(1)
		go func(taskID int) {
			defer wg.Done()
			executeTask(taskID, 10*time.Millisecond, "自由任务")
		}(i)
	}
	
	wg.Wait()
	duration := time.Since(start)
	
	printWorkloadStats("正常工作窃取", duration)
}

// 执行任务并记录统计信息
func executeTask(taskID int, duration time.Duration, taskType string) {
	start := time.Now()
	
	// 模拟任务执行
	time.Sleep(duration)
	
	// 简单的CPU工作
	sum := 0
	for i := 0; i < 1000; i++ {
		sum += i
	}
	
	elapsed := time.Since(start)
	
	// 记录统计信息（简化版本，实际无法准确获取当前P的ID）
	atomic.AddInt64(&globalTaskCount, 1)
	
	// 偶尔打印任务执行信息
	if taskID%20 == 0 {
		fmt.Printf("%s %d 执行完成，耗时: %v\n", taskType, taskID, elapsed)
	}
}

// 在特定处理器上执行任务（模拟）
func executeTaskOnProcessor(procID, taskID int, duration time.Duration, taskType string) {
	start := time.Now()
	
	// 模拟任务执行
	time.Sleep(duration)
	
	elapsed := time.Since(start)
	
	// 更新统计信息
	statsMutex.Lock()
	if stats, exists := processorStats[procID]; exists {
		atomic.AddInt64(&stats.TaskCount, 1)
		stats.TotalTime += elapsed
	}
	statsMutex.Unlock()
	
	atomic.AddInt64(&globalTaskCount, 1)
}

// 重置统计信息
func resetStats() {
	statsMutex.Lock()
	defer statsMutex.Unlock()
	
	for _, stats := range processorStats {
		atomic.StoreInt64(&stats.TaskCount, 0)
		stats.TotalTime = 0
	}
	atomic.StoreInt64(&globalTaskCount, 0)
}

// 打印负载统计信息
func printWorkloadStats(scenario string, totalDuration time.Duration) {
	fmt.Printf("\n%s 统计结果:\n", scenario)
	fmt.Printf("总执行时间: %v\n", totalDuration)
	fmt.Printf("总任务数: %d\n", atomic.LoadInt64(&globalTaskCount))
	fmt.Printf("平均任务执行时间: %v\n", totalDuration/time.Duration(atomic.LoadInt64(&globalTaskCount)))
	
	// 打印每个处理器的统计信息
	statsMutex.RLock()
	defer statsMutex.RUnlock()
	
	fmt.Println("各处理器负载分布:")
	for i := 0; i < len(processorStats); i++ {
		if stats, exists := processorStats[i]; exists {
			taskCount := atomic.LoadInt64(&stats.TaskCount)
			if taskCount > 0 {
				avgTime := stats.TotalTime / time.Duration(taskCount)
				fmt.Printf("  P%d: %d个任务, 平均耗时: %v\n", i, taskCount, avgTime)
			} else {
				fmt.Printf("  P%d: 0个任务\n", i)
			}
		}
	}
	
	// 计算负载均衡度
	calculateLoadBalance()
}

// 计算负载均衡度
func calculateLoadBalance() {
	var taskCounts []int64
	var totalTasks int64
	
	for i := 0; i < len(processorStats); i++ {
		if stats, exists := processorStats[i]; exists {
			count := atomic.LoadInt64(&stats.TaskCount)
			taskCounts = append(taskCounts, count)
			totalTasks += count
		}
	}
	
	if len(taskCounts) == 0 || totalTasks == 0 {
		return
	}
	
	// 计算标准差来衡量负载均衡度
	avgTasks := float64(totalTasks) / float64(len(taskCounts))
	var variance float64
	
	for _, count := range taskCounts {
		diff := float64(count) - avgTasks
		variance += diff * diff
	}
	
	variance /= float64(len(taskCounts))
	stdDev := variance // 简化计算，不开平方根
	
	fmt.Printf("负载均衡度 (方差): %.2f (越小越均衡)\n", stdDev)
}

// 多路复用器：将多个输入channel合并到一个输出channel
func multiplexer(output chan<- string, inputs ...<-chan string) {
	var wg sync.WaitGroup

	// 为每个输入channel启动一个转发Goroutine
	// 这是比使用reflect更高效和常见的做法
	for i, input := range inputs {
		wg.Add(1)
		go func(id int, ch <-chan string) {
			defer wg.Done()
			for msg := range ch {
				// 添加来源标识，便于观察工作窃取效果
				output <- fmt.Sprintf("[P%d] %s", id, msg)
			}
		}(i, input)
	}

	// 等待所有输入channel关闭后，关闭输出channel
	go func() {
		wg.Wait()
		close(output)
	}()
}

/*
工作窃取算法要点：

1. 本地队列优先：每个P优先执行自己本地队列中的G
2. 全局队列轮询：定期检查全局队列中的G
3. 工作窃取：当本地队列为空时，从其他P的队列中窃取G
4. 随机窃取：随机选择目标P，避免竞争热点
5. 批量窃取：一次窃取多个G，减少窃取频率

运行建议：
- 使用不同的GOMAXPROCS值观察效果
- 通过GODEBUG=schedtrace=1000观察调度器行为
- 修改任务数量和执行时间观察负载均衡效果
*/
