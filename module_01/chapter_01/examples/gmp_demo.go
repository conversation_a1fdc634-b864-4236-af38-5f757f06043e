// GMP模型演示程序
// 本程序展示Goroutine(G)、Machine(M)、Processor(P)三者的关系和交互
package main

import (
	"fmt"
	"runtime"
	"sync"
	"time"
)

// 演示GMP模型的基本概念
func main() {
	fmt.Println("=== GMP模型演示 ===")
	
	// 获取当前系统信息
	printSystemInfo()
	
	// 演示1：观察P的数量对并发的影响
	demonstrateProcessorCount()
	
	// 演示2：观察M的创建和复用
	demonstrateMachineCreation()
	
	// 演示3：观察G的调度行为
	demonstrateGoroutineScheduling()
}

// 打印系统基本信息
func printSystemInfo() {
	fmt.Printf("CPU核心数: %d\n", runtime.NumCPU())
	fmt.Printf("GOMAXPROCS: %d\n", runtime.GOMAXPROCS(0))
	fmt.Printf("当前Goroutine数: %d\n", runtime.NumGoroutine())
	fmt.Println()
}

// 演示P的数量对并发性能的影响
func demonstrateProcessorCount() {
	fmt.Println("--- 演示1：P的数量对并发的影响 ---")
	
	// 测试不同GOMAXPROCS设置下的性能
	testCases := []int{1, 2, 4, runtime.NumCPU()}
	
	for _, procs := range testCases {
		runtime.GOMAXPROCS(procs)
		
		start := time.Now()
		var wg sync.WaitGroup
		
		// 创建CPU密集型任务
		for i := 0; i < 8; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()
				cpuIntensiveTask(id, 100000000)
			}(i)
		}
		
		wg.Wait()
		duration := time.Since(start)
		
		fmt.Printf("GOMAXPROCS=%d, 执行时间: %v\n", procs, duration)
	}
	fmt.Println()
}

// CPU密集型任务
func cpuIntensiveTask(id int, iterations int) {
	sum := 0
	for i := 0; i < iterations; i++ {
		sum += i
	}
	// 避免编译器优化
	_ = sum
}

// 演示M的创建和复用机制
func demonstrateMachineCreation() {
	fmt.Println("--- 演示2：M的创建和复用 ---")
	
	// 重置GOMAXPROCS
	runtime.GOMAXPROCS(runtime.NumCPU())
	
	var wg sync.WaitGroup
	
	// 创建大量阻塞的Goroutine来观察M的创建
	for i := 0; i < 20; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			// 模拟系统调用阻塞
			time.Sleep(100 * time.Millisecond)
			fmt.Printf("Goroutine %d 完成\n", id)
		}(i)
		
		// 每创建几个Goroutine就检查一次状态
		if i%5 == 0 {
			printRuntimeStats()
		}
	}
	
	wg.Wait()
	fmt.Println("所有Goroutine完成")
	printRuntimeStats()
	fmt.Println()
}

// 演示G的调度行为
func demonstrateGoroutineScheduling() {
	fmt.Println("--- 演示3：G的调度行为 ---")
	
	var wg sync.WaitGroup
	
	// 创建不同类型的Goroutine
	for i := 0; i < 5; i++ {
		wg.Add(3)
		
		// CPU密集型Goroutine
		go func(id int) {
			defer wg.Done()
			fmt.Printf("CPU密集型任务 %d 开始\n", id)
			cpuIntensiveTask(id, 50000000)
			fmt.Printf("CPU密集型任务 %d 完成\n", id)
		}(i)
		
		// I/O密集型Goroutine
		go func(id int) {
			defer wg.Done()
			fmt.Printf("I/O密集型任务 %d 开始\n", id)
			time.Sleep(50 * time.Millisecond)
			fmt.Printf("I/O密集型任务 %d 完成\n", id)
		}(i)
		
		// 协作式让出CPU的Goroutine
		go func(id int) {
			defer wg.Done()
			fmt.Printf("协作式任务 %d 开始\n", id)
			for j := 0; j < 10; j++ {
				// 主动让出CPU
				runtime.Gosched()
				time.Sleep(5 * time.Millisecond)
			}
			fmt.Printf("协作式任务 %d 完成\n", id)
		}(i)
	}
	
	wg.Wait()
	fmt.Println()
}

// 打印运行时统计信息
func printRuntimeStats() {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)
	
	fmt.Printf("当前Goroutine数: %d\n", runtime.NumGoroutine())
	fmt.Printf("当前系统线程数: %d\n", runtime.NumCgoCall()) // 近似值
	fmt.Printf("堆内存使用: %d KB\n", m.Alloc/1024)
}

// 高级演示：观察调度器的详细行为
func demonstrateSchedulerDetails() {
	fmt.Println("--- 高级演示：调度器详细行为 ---")
	
	// 设置调度器跟踪
	// 注意：这需要设置环境变量 GODEBUG=schedtrace=1000
	
	var wg sync.WaitGroup
	
	// 创建不同优先级的任务
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			// 模拟不同长度的任务
			if id%2 == 0 {
				// 短任务
				time.Sleep(10 * time.Millisecond)
			} else {
				// 长任务
				cpuIntensiveTask(id, 10000000)
			}
			
			fmt.Printf("任务 %d 在处理器 %d 上完成\n", id, getCurrentP())
		}(i)
	}
	
	wg.Wait()
}

// 获取当前P的ID（简化版本）
func getCurrentP() int {
	// 注意：这是一个简化的实现，实际的P ID获取需要更复杂的方法
	return runtime.GOMAXPROCS(0) % runtime.NumCPU()
}

// 使用示例和注释说明
/*
运行此程序时，可以通过以下环境变量观察更多调度器信息：

1. 查看调度器跟踪信息：
   GODEBUG=schedtrace=1000 go run gmp_demo.go

2. 查看GC和调度器信息：
   GODEBUG=schedtrace=1000,scheddetail=1 go run gmp_demo.go

3. 限制P的数量：
   GOMAXPROCS=2 go run gmp_demo.go

输出解释：
- schedtrace输出格式：SCHED 1004ms: gomaxprocs=4 idleprocs=0 threads=5 spinningthreads=0 idlethreads=0 runqueue=0 [4 0 0 0]
- gomaxprocs: P的数量
- idleprocs: 空闲的P数量
- threads: 总线程数(M)
- runqueue: 全局运行队列中的G数量
- [4 0 0 0]: 每个P的本地运行队列中的G数量
*/
