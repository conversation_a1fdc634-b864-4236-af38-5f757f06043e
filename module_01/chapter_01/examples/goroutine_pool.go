// Goroutine池实现示例
// 展示如何实现高效的Goroutine池来管理并发任务
package main

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

// Task 表示一个要执行的任务
type Task func() error

// Result 表示任务执行结果
type Result struct {
	TaskID int
	Error  error
	Duration time.Duration
}

// WorkerPool Goroutine池结构
type WorkerPool struct {
	// 配置参数
	workerCount int           // 工作者数量
	queueSize   int           // 任务队列大小
	
	// 内部状态
	taskQueue   chan Task     // 任务队列
	resultQueue chan Result   // 结果队列
	wg          sync.WaitGroup // 等待组
	ctx         context.Context
	cancel      context.CancelFunc
	
	// 统计信息
	tasksSubmitted int64 // 提交的任务数
	tasksCompleted int64 // 完成的任务数
	tasksError     int64 // 错误的任务数
	
	// 状态标志
	started int32 // 是否已启动
	stopped int32 // 是否已停止
}

// NewWorkerPool 创建新的工作者池
func NewWorkerPool(workerCount, queueSize int) *WorkerPool {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &WorkerPool{
		workerCount: workerCount,
		queueSize:   queueSize,
		taskQueue:   make(chan Task, queueSize),
		resultQueue: make(chan Result, queueSize),
		ctx:         ctx,
		cancel:      cancel,
	}
}

// Start 启动工作者池
func (wp *WorkerPool) Start() error {
	if !atomic.CompareAndSwapInt32(&wp.started, 0, 1) {
		return fmt.Errorf("工作者池已经启动")
	}
	
	fmt.Printf("启动工作者池: %d个工作者, 队列大小: %d\n", wp.workerCount, wp.queueSize)
	
	// 启动工作者Goroutine
	for i := 0; i < wp.workerCount; i++ {
		wp.wg.Add(1)
		go wp.worker(i)
	}
	
	// 启动结果处理Goroutine
	go wp.resultHandler()
	
	return nil
}

// Stop 停止工作者池
func (wp *WorkerPool) Stop() {
	if !atomic.CompareAndSwapInt32(&wp.stopped, 0, 1) {
		return
	}
	
	fmt.Println("正在停止工作者池...")
	
	// 关闭任务队列，不再接受新任务
	close(wp.taskQueue)
	
	// 等待所有工作者完成
	wp.wg.Wait()
	
	// 取消上下文
	wp.cancel()
	
	// 关闭结果队列
	close(wp.resultQueue)
	
	fmt.Println("工作者池已停止")
}

// Submit 提交任务到池中
func (wp *WorkerPool) Submit(task Task) error {
	if atomic.LoadInt32(&wp.stopped) == 1 {
		return fmt.Errorf("工作者池已停止")
	}
	
	select {
	case wp.taskQueue <- task:
		atomic.AddInt64(&wp.tasksSubmitted, 1)
		return nil
	case <-wp.ctx.Done():
		return fmt.Errorf("工作者池已关闭")
	default:
		return fmt.Errorf("任务队列已满")
	}
}

// SubmitWithTimeout 带超时的任务提交
func (wp *WorkerPool) SubmitWithTimeout(task Task, timeout time.Duration) error {
	if atomic.LoadInt32(&wp.stopped) == 1 {
		return fmt.Errorf("工作者池已停止")
	}
	
	ctx, cancel := context.WithTimeout(wp.ctx, timeout)
	defer cancel()
	
	select {
	case wp.taskQueue <- task:
		atomic.AddInt64(&wp.tasksSubmitted, 1)
		return nil
	case <-ctx.Done():
		return fmt.Errorf("提交任务超时")
	}
}

// worker 工作者Goroutine
func (wp *WorkerPool) worker(id int) {
	defer wp.wg.Done()
	
	fmt.Printf("工作者 %d 启动\n", id)
	
	for {
		select {
		case task, ok := <-wp.taskQueue:
			if !ok {
				fmt.Printf("工作者 %d 退出\n", id)
				return
			}
			
			// 执行任务
			wp.executeTask(id, task)
			
		case <-wp.ctx.Done():
			fmt.Printf("工作者 %d 被取消\n", id)
			return
		}
	}
}

// executeTask 执行单个任务
func (wp *WorkerPool) executeTask(workerID int, task Task) {
	start := time.Now()
	taskID := int(atomic.LoadInt64(&wp.tasksCompleted) + 1)
	
	fmt.Printf("工作者 %d 开始执行任务 %d\n", workerID, taskID)
	
	// 执行任务
	err := task()
	duration := time.Since(start)
	
	// 更新统计
	atomic.AddInt64(&wp.tasksCompleted, 1)
	if err != nil {
		atomic.AddInt64(&wp.tasksError, 1)
		fmt.Printf("工作者 %d 任务 %d 执行失败: %v (耗时: %v)\n", 
			workerID, taskID, err, duration)
	} else {
		fmt.Printf("工作者 %d 任务 %d 执行成功 (耗时: %v)\n", 
			workerID, taskID, duration)
	}
	
	// 发送结果
	result := Result{
		TaskID:   taskID,
		Error:    err,
		Duration: duration,
	}
	
	select {
	case wp.resultQueue <- result:
	default:
		// 结果队列满，丢弃结果（或者可以选择阻塞）
		fmt.Printf("结果队列已满，丢弃任务 %d 的结果\n", taskID)
	}
}

// resultHandler 结果处理器
func (wp *WorkerPool) resultHandler() {
	fmt.Println("结果处理器启动")
	
	for result := range wp.resultQueue {
		// 这里可以处理结果，比如记录日志、发送通知等
		if result.Error != nil {
			fmt.Printf("任务 %d 失败: %v\n", result.TaskID, result.Error)
		}
	}
	
	fmt.Println("结果处理器退出")
}

// GetStats 获取统计信息
func (wp *WorkerPool) GetStats() (submitted, completed, errors int64) {
	return atomic.LoadInt64(&wp.tasksSubmitted),
		   atomic.LoadInt64(&wp.tasksCompleted),
		   atomic.LoadInt64(&wp.tasksError)
}

// GetQueueSize 获取当前队列大小
func (wp *WorkerPool) GetQueueSize() int {
	return len(wp.taskQueue)
}

// 动态工作者池 - 支持动态调整工作者数量
type DynamicWorkerPool struct {
	*WorkerPool
	minWorkers    int
	maxWorkers    int
	currentWorkers int32
	mu            sync.RWMutex
}

// NewDynamicWorkerPool 创建动态工作者池
func NewDynamicWorkerPool(minWorkers, maxWorkers, queueSize int) *DynamicWorkerPool {
	basePool := NewWorkerPool(minWorkers, queueSize)
	
	return &DynamicWorkerPool{
		WorkerPool:     basePool,
		minWorkers:     minWorkers,
		maxWorkers:     maxWorkers,
		currentWorkers: int32(minWorkers),
	}
}

// ScaleUp 增加工作者
func (dwp *DynamicWorkerPool) ScaleUp(count int) error {
	dwp.mu.Lock()
	defer dwp.mu.Unlock()
	
	current := atomic.LoadInt32(&dwp.currentWorkers)
	if int(current)+count > dwp.maxWorkers {
		return fmt.Errorf("超过最大工作者数量限制")
	}
	
	for i := 0; i < count; i++ {
		dwp.wg.Add(1)
		workerID := int(atomic.AddInt32(&dwp.currentWorkers, 1))
		go dwp.worker(workerID)
		fmt.Printf("新增工作者 %d\n", workerID)
	}
	
	return nil
}

// ScaleDown 减少工作者（通过取消上下文实现）
func (dwp *DynamicWorkerPool) ScaleDown(count int) error {
	dwp.mu.Lock()
	defer dwp.mu.Unlock()
	
	current := atomic.LoadInt32(&dwp.currentWorkers)
	if int(current)-count < dwp.minWorkers {
		return fmt.Errorf("低于最小工作者数量限制")
	}
	
	// 注意：这是一个简化实现，实际中需要更精细的控制
	fmt.Printf("请求减少 %d 个工作者（当前实现会在任务完成后自然退出）\n", count)
	
	return nil
}

// 示例使用
func main() {
	fmt.Println("=== Goroutine池实现示例 ===")
	
	// 基本工作者池示例
	demonstrateBasicWorkerPool()
	
	// 动态工作者池示例
	demonstrateDynamicWorkerPool()
	
	// 性能对比示例
	demonstratePerformanceComparison()
}

// 演示基本工作者池
func demonstrateBasicWorkerPool() {
	fmt.Println("\n--- 基本工作者池演示 ---")
	
	// 创建工作者池
	pool := NewWorkerPool(4, 10)
	
	// 启动池
	if err := pool.Start(); err != nil {
		fmt.Printf("启动失败: %v\n", err)
		return
	}
	
	// 提交任务
	for i := 0; i < 20; i++ {
		taskID := i
		task := func(id int) func() error {
			return func() error {
				// 模拟不同长度的任务
				sleepTime := time.Duration(50+id*10) * time.Millisecond
				time.Sleep(sleepTime)

				// 模拟一些任务失败
				if id%7 == 0 {
					return fmt.Errorf("任务 %d 模拟失败", id)
				}

				return nil
			}
		}(taskID)
		
		if err := pool.Submit(task); err != nil {
			fmt.Printf("提交任务 %d 失败: %v\n", taskID, err)
		}
	}
	
	// 等待一段时间让任务执行
	time.Sleep(2 * time.Second)
	
	// 打印统计信息
	submitted, completed, errors := pool.GetStats()
	fmt.Printf("统计信息 - 提交: %d, 完成: %d, 错误: %d, 队列大小: %d\n", 
		submitted, completed, errors, pool.GetQueueSize())
	
	// 停止池
	pool.Stop()
}

// 演示动态工作者池
func demonstrateDynamicWorkerPool() {
	fmt.Println("\n--- 动态工作者池演示 ---")
	
	// 创建动态工作者池
	pool := NewDynamicWorkerPool(2, 8, 20)
	
	// 启动池
	if err := pool.Start(); err != nil {
		fmt.Printf("启动失败: %v\n", err)
		return
	}
	
	// 提交初始任务
	for i := 0; i < 10; i++ {
		task := func() error {
			time.Sleep(100 * time.Millisecond)
			return nil
		}
		pool.Submit(task)
	}
	
	time.Sleep(200 * time.Millisecond)
	
	// 扩展工作者
	fmt.Println("扩展工作者...")
	pool.ScaleUp(3)
	
	// 提交更多任务
	for i := 10; i < 30; i++ {
		task := func() error {
			time.Sleep(50 * time.Millisecond)
			return nil
		}
		pool.Submit(task)
	}
	
	// 等待任务完成
	time.Sleep(1 * time.Second)
	
	// 打印统计信息
	submitted, completed, errors := pool.GetStats()
	fmt.Printf("动态池统计 - 提交: %d, 完成: %d, 错误: %d\n", 
		submitted, completed, errors)
	
	// 停止池
	pool.Stop()
}

// 演示性能对比
func demonstratePerformanceComparison() {
	fmt.Println("\n--- 性能对比演示 ---")
	
	const taskCount = 1000
	
	// 测试1：无池化，每个任务创建新Goroutine
	start := time.Now()
	var wg sync.WaitGroup
	
	for i := 0; i < taskCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			time.Sleep(time.Millisecond)
		}()
	}
	wg.Wait()
	
	nopoolDuration := time.Since(start)
	fmt.Printf("无池化执行 %d 任务: %v\n", taskCount, nopoolDuration)
	
	// 测试2：使用工作者池
	start = time.Now()
	pool := NewWorkerPool(runtime.NumCPU(), taskCount)
	pool.Start()
	
	for i := 0; i < taskCount; i++ {
		task := func() error {
			time.Sleep(time.Millisecond)
			return nil
		}
		pool.Submit(task)
	}
	
	// 等待所有任务完成
	for {
		_, completed, _ := pool.GetStats()
		if completed >= taskCount {
			break
		}
		time.Sleep(10 * time.Millisecond)
	}
	
	poolDuration := time.Since(start)
	pool.Stop()
	
	fmt.Printf("工作者池执行 %d 任务: %v\n", taskCount, poolDuration)
	fmt.Printf("性能提升: %.2fx\n", float64(nopoolDuration)/float64(poolDuration))
}

/*
Goroutine池的优势：

1. 资源控制：
   - 限制并发Goroutine数量
   - 避免系统资源耗尽
   - 可预测的内存使用

2. 性能优化：
   - 减少Goroutine创建/销毁开销
   - 复用工作者Goroutine
   - 更好的CPU缓存局部性

3. 任务管理：
   - 任务队列缓冲
   - 优雅的关闭机制
   - 错误处理和统计

4. 可扩展性：
   - 动态调整工作者数量
   - 负载均衡
   - 监控和指标收集

使用场景：
- Web服务器请求处理
- 批量数据处理
- 文件处理任务
- 网络爬虫
- 图像/视频处理

最佳实践：
- 根据CPU核心数设置工作者数量
- 合理设置队列大小
- 实现优雅关闭机制
- 添加监控和指标
- 处理任务执行错误
*/
