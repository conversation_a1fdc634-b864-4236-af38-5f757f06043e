// 调度器监控工具
// 提供实时监控Go调度器状态的工具和方法
package main

import (
	"context"
	"fmt"
	"os"
	"runtime"
	"runtime/debug"
	"runtime/trace"
	"sync"
	"time"
)

// SchedulerMonitor 调度器监控器
type SchedulerMonitor struct {
	ctx        context.Context
	cancel     context.CancelFunc
	interval   time.Duration
	mu         sync.RWMutex
	stats      []SchedulerSnapshot
	maxHistory int
}

// SchedulerSnapshot 调度器快照
type SchedulerSnapshot struct {
	Timestamp      time.Time
	NumGoroutine   int
	NumCPU         int
	GOMAXPROCS     int
	NumCgoCall     int64
	MemStats       runtime.MemStats
	GCStats        debug.GCStats
}

// NewSchedulerMonitor 创建新的调度器监控器
func NewSchedulerMonitor(interval time.Duration, maxHistory int) *SchedulerMonitor {
	ctx, cancel := context.WithCancel(context.Background())
	
	return &SchedulerMonitor{
		ctx:        ctx,
		cancel:     cancel,
		interval:   interval,
		maxHistory: maxHistory,
		stats:      make([]SchedulerSnapshot, 0, maxHistory),
	}
}

// Start 开始监控
func (sm *SchedulerMonitor) Start() {
	go sm.monitorLoop()
	fmt.Printf("调度器监控已启动，监控间隔: %v\n", sm.interval)
}

// Stop 停止监控
func (sm *SchedulerMonitor) Stop() {
	sm.cancel()
	fmt.Println("调度器监控已停止")
}

// monitorLoop 监控循环
func (sm *SchedulerMonitor) monitorLoop() {
	ticker := time.NewTicker(sm.interval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			sm.collectSnapshot()
		case <-sm.ctx.Done():
			return
		}
	}
}

// collectSnapshot 收集调度器快照
func (sm *SchedulerMonitor) collectSnapshot() {
	snapshot := SchedulerSnapshot{
		Timestamp:    time.Now(),
		NumGoroutine: runtime.NumGoroutine(),
		NumCPU:       runtime.NumCPU(),
		GOMAXPROCS:   runtime.GOMAXPROCS(0),
		NumCgoCall:   runtime.NumCgoCall(),
	}
	
	// 收集内存统计
	runtime.ReadMemStats(&snapshot.MemStats)
	
	// 收集GC统计
	debug.ReadGCStats(&snapshot.GCStats)
	
	sm.mu.Lock()
	defer sm.mu.Unlock()
	
	// 添加到历史记录
	sm.stats = append(sm.stats, snapshot)
	
	// 保持历史记录在限制范围内
	if len(sm.stats) > sm.maxHistory {
		sm.stats = sm.stats[1:]
	}
}

// GetCurrentSnapshot 获取当前快照
func (sm *SchedulerMonitor) GetCurrentSnapshot() SchedulerSnapshot {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	
	if len(sm.stats) == 0 {
		sm.collectSnapshot()
	}
	
	return sm.stats[len(sm.stats)-1]
}

// GetHistory 获取历史快照
func (sm *SchedulerMonitor) GetHistory() []SchedulerSnapshot {
	sm.mu.RLock()
	defer sm.mu.RUnlock()
	
	// 返回副本
	history := make([]SchedulerSnapshot, len(sm.stats))
	copy(history, sm.stats)
	return history
}

// PrintCurrentStats 打印当前统计信息
func (sm *SchedulerMonitor) PrintCurrentStats() {
	snapshot := sm.GetCurrentSnapshot()
	
	fmt.Printf("\n=== 调度器状态 (%v) ===\n", snapshot.Timestamp.Format("15:04:05"))
	fmt.Printf("Goroutine数量: %d\n", snapshot.NumGoroutine)
	fmt.Printf("CPU核心数: %d\n", snapshot.NumCPU)
	fmt.Printf("GOMAXPROCS: %d\n", snapshot.GOMAXPROCS)
	fmt.Printf("CGO调用次数: %d\n", snapshot.NumCgoCall)
	
	// 内存信息
	fmt.Printf("\n--- 内存统计 ---\n")
	fmt.Printf("堆内存分配: %d KB\n", snapshot.MemStats.Alloc/1024)
	fmt.Printf("总分配内存: %d KB\n", snapshot.MemStats.TotalAlloc/1024)
	fmt.Printf("系统内存: %d KB\n", snapshot.MemStats.Sys/1024)
	fmt.Printf("堆对象数: %d\n", snapshot.MemStats.HeapObjects)
	
	// GC信息
	fmt.Printf("\n--- GC统计 ---\n")
	fmt.Printf("GC次数: %d\n", snapshot.MemStats.NumGC)
	fmt.Printf("GC暂停时间: %v\n", time.Duration(snapshot.MemStats.PauseTotalNs))
	if len(snapshot.GCStats.Pause) > 0 {
		fmt.Printf("最近GC暂停: %v\n", snapshot.GCStats.Pause[0])
	}
}

// PrintTrend 打印趋势分析
func (sm *SchedulerMonitor) PrintTrend() {
	history := sm.GetHistory()
	if len(history) < 2 {
		fmt.Println("历史数据不足，无法分析趋势")
		return
	}
	
	first := history[0]
	last := history[len(history)-1]
	duration := last.Timestamp.Sub(first.Timestamp)
	
	fmt.Printf("\n=== 趋势分析 (过去 %v) ===\n", duration)
	
	// Goroutine数量趋势
	goroutineDiff := last.NumGoroutine - first.NumGoroutine
	fmt.Printf("Goroutine变化: %+d (从 %d 到 %d)\n", 
		goroutineDiff, first.NumGoroutine, last.NumGoroutine)
	
	// 内存趋势
	memDiff := int64(last.MemStats.Alloc) - int64(first.MemStats.Alloc)
	fmt.Printf("堆内存变化: %+d KB (从 %d 到 %d KB)\n", 
		memDiff/1024, first.MemStats.Alloc/1024, last.MemStats.Alloc/1024)
	
	// GC趋势
	gcDiff := last.MemStats.NumGC - first.MemStats.NumGC
	if gcDiff > 0 {
		avgGCInterval := duration / time.Duration(gcDiff)
		fmt.Printf("GC频率: %d次, 平均间隔: %v\n", gcDiff, avgGCInterval)
	}
	
	// CGO调用趋势
	cgoDiff := last.NumCgoCall - first.NumCgoCall
	if cgoDiff > 0 {
		fmt.Printf("CGO调用增加: %d次\n", cgoDiff)
	}
}

// 实时监控显示
func (sm *SchedulerMonitor) StartRealTimeDisplay() {
	go func() {
		ticker := time.NewTicker(2 * time.Second)
		defer ticker.Stop()
		
		for {
			select {
			case <-ticker.C:
				// 清屏（简单实现）
				fmt.Print("\033[2J\033[H")
				sm.PrintCurrentStats()
				sm.PrintTrend()
			case <-sm.ctx.Done():
				return
			}
		}
	}()
}

// 调度器压力测试
func runSchedulerStressTest(monitor *SchedulerMonitor) {
	fmt.Println("\n=== 开始调度器压力测试 ===")
	
	var wg sync.WaitGroup
	
	// 测试1：大量短生命周期Goroutine
	fmt.Println("测试1: 大量短生命周期Goroutine")
	for i := 0; i < 1000; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			time.Sleep(time.Millisecond * time.Duration(id%10+1))
		}(i)
	}
	
	time.Sleep(2 * time.Second)
	monitor.PrintCurrentStats()
	
	// 测试2：CPU密集型任务
	fmt.Println("\n测试2: CPU密集型任务")
	for i := 0; i < runtime.NumCPU(); i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			sum := 0
			for j := 0; j < 100000000; j++ {
				sum += j
			}
			_ = sum
		}()
	}
	
	time.Sleep(3 * time.Second)
	monitor.PrintCurrentStats()
	
	// 测试3：I/O密集型任务
	fmt.Println("\n测试3: I/O密集型任务")
	for i := 0; i < 50; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			time.Sleep(100 * time.Millisecond)
		}()
	}
	
	time.Sleep(2 * time.Second)
	monitor.PrintCurrentStats()
	
	wg.Wait()
	fmt.Println("\n压力测试完成")
}

// 生成调度器跟踪文件
func generateSchedulerTrace() {
	fmt.Println("\n=== 生成调度器跟踪文件 ===")
	
	// 创建跟踪文件
	f, err := os.Create("scheduler_trace.out")
	if err != nil {
		fmt.Printf("创建跟踪文件失败: %v\n", err)
		return
	}
	defer f.Close()
	
	// 开始跟踪
	if err := trace.Start(f); err != nil {
		fmt.Printf("开始跟踪失败: %v\n", err)
		return
	}
	defer trace.Stop()
	
	fmt.Println("开始记录调度器跟踪...")
	
	// 执行一些有代表性的工作负载
	var wg sync.WaitGroup
	
	// 创建一些Goroutine
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			// 混合工作负载
			for j := 0; j < 5; j++ {
				// CPU工作
				sum := 0
				for k := 0; k < 1000000; k++ {
					sum += k
				}
				
				// I/O工作
				time.Sleep(10 * time.Millisecond)
				
				// 协作让出
				runtime.Gosched()
			}
		}(i)
	}
	
	wg.Wait()
	
	fmt.Println("跟踪记录完成，文件: scheduler_trace.out")
	fmt.Println("使用以下命令查看跟踪结果:")
	fmt.Println("go tool trace scheduler_trace.out")
}

// 主函数演示
func main() {
	fmt.Println("=== Go调度器监控工具 ===")
	
	// 创建监控器
	monitor := NewSchedulerMonitor(500*time.Millisecond, 100)
	
	// 启动监控
	monitor.Start()
	defer monitor.Stop()
	
	// 启动实时显示（可选）
	// monitor.StartRealTimeDisplay()
	
	// 运行一些示例工作负载
	fmt.Println("运行示例工作负载...")
	
	var wg sync.WaitGroup
	
	// 创建一些背景Goroutine
	for i := 0; i < 5; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			for j := 0; j < 10; j++ {
				// 模拟工作
				time.Sleep(time.Duration(100+id*50) * time.Millisecond)
				
				// 偶尔触发GC
				if j%3 == 0 {
					runtime.GC()
				}
			}
		}(i)
	}
	
	// 定期打印统计信息
	go func() {
		ticker := time.NewTicker(2 * time.Second)
		defer ticker.Stop()
		
		for i := 0; i < 5; i++ {
			<-ticker.C
			monitor.PrintCurrentStats()
		}
	}()
	
	wg.Wait()
	
	// 打印最终趋势分析
	monitor.PrintTrend()
	
	// 运行压力测试
	runSchedulerStressTest(monitor)
	
	// 生成跟踪文件
	generateSchedulerTrace()
	
	fmt.Println("\n监控演示完成")
}

/*
调度器监控工具使用说明：

1. 基本监控：
   - 实时监控Goroutine数量
   - 内存使用情况
   - GC统计信息
   - 系统资源使用

2. 趋势分析：
   - Goroutine数量变化趋势
   - 内存使用趋势
   - GC频率分析
   - 性能指标变化

3. 调试工具：
   - GODEBUG环境变量
   - runtime/trace包
   - runtime/pprof包
   - go tool trace

4. 环境变量设置：
   export GODEBUG=schedtrace=1000,scheddetail=1
   go run scheduler_monitor.go

5. 跟踪分析：
   go tool trace scheduler_trace.out

6. 性能分析：
   go tool pprof http://localhost:6060/debug/pprof/goroutine

监控指标说明：
- NumGoroutine: 当前Goroutine数量
- GOMAXPROCS: 逻辑处理器数量
- Alloc: 当前堆内存分配
- TotalAlloc: 累计内存分配
- NumGC: GC执行次数
- PauseTotalNs: GC总暂停时间

性能调优建议：
1. 监控Goroutine泄漏
2. 优化GC频率和暂停时间
3. 合理设置GOMAXPROCS
4. 避免过度的内存分配
5. 使用对象池减少GC压力
*/
