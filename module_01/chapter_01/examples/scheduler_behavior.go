// 调度器行为观察示例
// 通过不同的实验观察Go调度器的具体行为模式
package main

import (
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

// 调度器统计信息
type SchedulerStats struct {
	GoroutineCount    int64
	ScheduleCount     int64
	PreemptCount      int64
	YieldCount        int64
	BlockCount        int64
	UnblockCount      int64
}

var (
	globalStats SchedulerStats
)

func main() {
	fmt.Println("=== Go调度器行为观察 ===")
	
	// 设置合适的GOMAXPROCS
	runtime.GOMAXPROCS(runtime.NumCPU())
	
	fmt.Printf("CPU核心数: %d\n", runtime.NumCPU())
	fmt.Printf("GOMAXPROCS: %d\n", runtime.GOMAXPROCS(0))
	fmt.Println()
	
	// 实验1：观察协作式调度
	observeCooperativeScheduling()
	
	// 实验2：观察抢占式调度
	observePreemptiveScheduling()
	
	// 实验3：观察I/O阻塞调度
	observeIOBlockingScheduling()
	
	// 实验4：观察系统调用调度
	observeSyscallScheduling()
	
	// 实验5：观察Channel阻塞调度
	observeChannelBlockingScheduling()
	
	// 实验6：观察不同负载下的调度行为
	observeLoadBasedScheduling()
}

// 实验1：观察协作式调度行为
func observeCooperativeScheduling() {
	fmt.Println("--- 实验1：协作式调度观察 ---")
	
	var wg sync.WaitGroup
	startTime := time.Now()
	
	// 创建协作式任务
	for i := 0; i < 4; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			fmt.Printf("协作任务 %d 开始\n", id)
			
			// 执行一些工作，然后主动让出CPU
			for j := 0; j < 5; j++ {
				// 模拟一些计算工作
				sum := 0
				for k := 0; k < 1000000; k++ {
					sum += k
				}
				
				// 主动让出CPU，允许其他Goroutine运行
				runtime.Gosched()
				fmt.Printf("协作任务 %d 让出CPU (轮次 %d)\n", id, j+1)
				
				// 短暂休息
				time.Sleep(10 * time.Millisecond)
			}
			
			fmt.Printf("协作任务 %d 完成\n", id)
		}(i)
	}
	
	wg.Wait()
	fmt.Printf("协作式调度实验完成，耗时: %v\n\n", time.Since(startTime))
}

// 实验2：观察抢占式调度行为
func observePreemptiveScheduling() {
	fmt.Println("--- 实验2：抢占式调度观察 ---")
	
	var wg sync.WaitGroup
	var counter int64
	startTime := time.Now()
	
	// 创建CPU密集型任务，不主动让出CPU
	for i := 0; i < 4; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			fmt.Printf("CPU密集任务 %d 开始\n", id)
			localCounter := int64(0)
			
			// 长时间CPU密集计算，不主动让出CPU
			// 调度器会通过抢占式调度来切换Goroutine
			for j := 0; j < 100000000; j++ {
				localCounter++
				
				// 每隔一定次数检查是否被抢占
				if j%10000000 == 0 {
					fmt.Printf("CPU密集任务 %d 进度: %d%%\n", id, j/1000000)
				}
			}
			
			atomic.AddInt64(&counter, localCounter)
			fmt.Printf("CPU密集任务 %d 完成，计算结果: %d\n", id, localCounter)
		}(i)
	}
	
	wg.Wait()
	fmt.Printf("抢占式调度实验完成，总计算结果: %d，耗时: %v\n\n", 
		atomic.LoadInt64(&counter), time.Since(startTime))
}

// 实验3：观察I/O阻塞调度
func observeIOBlockingScheduling() {
	fmt.Println("--- 实验3：I/O阻塞调度观察 ---")
	
	var wg sync.WaitGroup
	startTime := time.Now()
	
	// 创建I/O密集型任务
	for i := 0; i < 6; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			fmt.Printf("I/O任务 %d 开始\n", id)
			
			// 模拟不同长度的I/O操作
			for j := 0; j < 3; j++ {
				fmt.Printf("I/O任务 %d 执行I/O操作 %d\n", id, j+1)
				
				// 模拟I/O阻塞（网络请求、文件读写等）
				sleepDuration := time.Duration(50+id*10) * time.Millisecond
				time.Sleep(sleepDuration)
				
				fmt.Printf("I/O任务 %d 完成I/O操作 %d\n", id, j+1)
			}
			
			fmt.Printf("I/O任务 %d 完成\n", id)
		}(i)
	}
	
	wg.Wait()
	fmt.Printf("I/O阻塞调度实验完成，耗时: %v\n\n", time.Since(startTime))
}

// 实验4：观察系统调用调度
func observeSyscallScheduling() {
	fmt.Println("--- 实验4：系统调用调度观察 ---")
	
	var wg sync.WaitGroup
	startTime := time.Now()
	
	// 创建涉及系统调用的任务
	for i := 0; i < 4; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			fmt.Printf("系统调用任务 %d 开始\n", id)
			
			for j := 0; j < 3; j++ {
				// 获取当前Goroutine数量（涉及系统调用）
				goroutineCount := runtime.NumGoroutine()
				
				// 强制GC（涉及系统调用）
				runtime.GC()
				
				// 获取内存统计（涉及系统调用）
				var m runtime.MemStats
				runtime.ReadMemStats(&m)
				
				fmt.Printf("系统调用任务 %d 轮次 %d: Goroutine数=%d, 堆内存=%dKB\n", 
					id, j+1, goroutineCount, m.Alloc/1024)
				
				// 短暂休息
				time.Sleep(20 * time.Millisecond)
			}
			
			fmt.Printf("系统调用任务 %d 完成\n", id)
		}(i)
	}
	
	wg.Wait()
	fmt.Printf("系统调用调度实验完成，耗时: %v\n\n", time.Since(startTime))
}

// 实验5：观察Channel阻塞调度
func observeChannelBlockingScheduling() {
	fmt.Println("--- 实验5：Channel阻塞调度观察 ---")
	
	ch := make(chan int, 2) // 小缓冲区
	var wg sync.WaitGroup
	startTime := time.Now()
	
	// 生产者Goroutine
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			fmt.Printf("生产者 %d 开始\n", id)
			
			for j := 0; j < 5; j++ {
				value := id*10 + j
				fmt.Printf("生产者 %d 尝试发送: %d\n", id, value)
				
				ch <- value // 可能阻塞
				
				fmt.Printf("生产者 %d 成功发送: %d\n", id, value)
				time.Sleep(30 * time.Millisecond)
			}
			
			fmt.Printf("生产者 %d 完成\n", id)
		}(i)
	}
	
	// 消费者Goroutine
	wg.Add(1)
	go func() {
		defer wg.Done()
		
		fmt.Println("消费者开始")
		
		for i := 0; i < 15; i++ { // 3个生产者 * 5个值
			fmt.Println("消费者等待接收...")
			
			value := <-ch // 可能阻塞
			
			fmt.Printf("消费者接收到: %d\n", value)
			
			// 模拟处理时间
			time.Sleep(50 * time.Millisecond)
		}
		
		fmt.Println("消费者完成")
	}()
	
	wg.Wait()
	fmt.Printf("Channel阻塞调度实验完成，耗时: %v\n\n", time.Since(startTime))
}

// 实验6：观察不同负载下的调度行为
func observeLoadBasedScheduling() {
	fmt.Println("--- 实验6：不同负载下的调度行为 ---")
	
	// 轻负载测试
	fmt.Println("轻负载测试:")
	testSchedulingUnderLoad("轻负载", 2, 50*time.Millisecond)
	
	// 中等负载测试
	fmt.Println("中等负载测试:")
	testSchedulingUnderLoad("中等负载", 8, 100*time.Millisecond)
	
	// 重负载测试
	fmt.Println("重负载测试:")
	testSchedulingUnderLoad("重负载", 20, 200*time.Millisecond)
}

// 在指定负载下测试调度行为
func testSchedulingUnderLoad(loadType string, numGoroutines int, workDuration time.Duration) {
	var wg sync.WaitGroup
	startTime := time.Now()
	
	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			// 混合工作负载
			for j := 0; j < 3; j++ {
				// CPU工作
				sum := 0
				for k := 0; k < 1000000; k++ {
					sum += k
				}
				
				// I/O工作
				time.Sleep(workDuration / 3)
				
				// 协作让出
				if j%2 == 0 {
					runtime.Gosched()
				}
			}
			
			if id%5 == 0 { // 只打印部分日志，避免输出过多
				fmt.Printf("%s: Goroutine %d 完成\n", loadType, id)
			}
		}(i)
	}
	
	wg.Wait()
	duration := time.Since(startTime)
	
	fmt.Printf("%s完成: %d个Goroutine，耗时: %v，平均每个: %v\n\n", 
		loadType, numGoroutines, duration, duration/time.Duration(numGoroutines))
}

/*
调度器行为观察要点：

1. 协作式调度：
   - Goroutine主动调用runtime.Gosched()让出CPU
   - 适用于长时间运行的计算任务
   - 可以提高系统响应性

2. 抢占式调度：
   - Go 1.14+支持基于信号的抢占
   - 防止单个Goroutine长时间占用CPU
   - 对CPU密集型任务特别重要

3. I/O阻塞调度：
   - Goroutine在I/O操作时自动让出M
   - 调度器会创建新的M来运行其他Goroutine
   - 提高I/O密集型应用的并发性

4. 系统调用调度：
   - 系统调用可能导致M阻塞
   - 调度器会将P转移到其他M
   - 保证系统整体的并发性能

5. Channel阻塞调度：
   - Channel操作可能导致Goroutine阻塞
   - 阻塞的Goroutine会被移出运行队列
   - 当Channel就绪时，Goroutine会被重新调度

运行建议：
- 使用GODEBUG=schedtrace=1000观察调度器统计信息
- 尝试不同的GOMAXPROCS值观察性能变化
- 使用go tool trace分析详细的调度行为
- 结合pprof分析CPU和内存使用情况
*/
