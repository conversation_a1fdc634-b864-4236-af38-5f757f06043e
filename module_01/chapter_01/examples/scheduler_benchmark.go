// 调度器性能基准测试
// 通过基准测试分析不同场景下调度器的性能表现
package main

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"testing"
	"time"
)

func main() {
	fmt.Println("=== Go调度器性能基准测试 ===")
	
	// 运行各种基准测试
	runGoroutineCreationBenchmark()
	runContextSwitchBenchmark()
	runChannelBenchmark()
	runMutexBenchmark()
	runDifferentGOMAXPROCSBenchmark()
	runScalabilityBenchmark()
}

// Goroutine创建性能基准测试
func runGoroutineCreationBenchmark() {
	fmt.Println("\n--- Goroutine创建性能测试 ---")
	
	// 测试不同数量的Goroutine创建性能
	testCases := []int{100, 1000, 10000, 100000}
	
	for _, count := range testCases {
		start := time.Now()
		var wg sync.WaitGroup
		
		for i := 0; i < count; i++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				// 最小工作量
				runtime.Gosched()
			}()
		}
		
		wg.Wait()
		duration := time.Since(start)
		
		fmt.Printf("创建 %d 个Goroutine: %v (平均 %v/个)\n", 
			count, duration, duration/time.Duration(count))
	}
}

// 上下文切换性能基准测试
func runContextSwitchBenchmark() {
	fmt.Println("\n--- 上下文切换性能测试 ---")
	
	// 测试Goroutine之间的上下文切换开销
	const iterations = 100000
	
	// 测试1：通过Channel进行上下文切换
	start := time.Now()
	ch1 := make(chan struct{})
	ch2 := make(chan struct{})
	
	go func() {
		for i := 0; i < iterations; i++ {
			<-ch1
			ch2 <- struct{}{}
		}
	}()
	
	for i := 0; i < iterations; i++ {
		ch1 <- struct{}{}
		<-ch2
	}
	
	channelDuration := time.Since(start)
	fmt.Printf("Channel上下文切换 %d 次: %v (平均 %v/次)\n", 
		iterations, channelDuration, channelDuration/iterations)
	
	// 测试2：通过runtime.Gosched()进行上下文切换
	start = time.Now()
	var counter int64
	var wg sync.WaitGroup
	
	wg.Add(2)
	go func() {
		defer wg.Done()
		for i := 0; i < iterations/2; i++ {
			atomic.AddInt64(&counter, 1)
			runtime.Gosched()
		}
	}()
	
	go func() {
		defer wg.Done()
		for i := 0; i < iterations/2; i++ {
			atomic.AddInt64(&counter, 1)
			runtime.Gosched()
		}
	}()
	
	wg.Wait()
	goschedDuration := time.Since(start)
	fmt.Printf("Gosched上下文切换 %d 次: %v (平均 %v/次)\n", 
		iterations, goschedDuration, goschedDuration/iterations)
}

// Channel性能基准测试
func runChannelBenchmark() {
	fmt.Println("\n--- Channel性能测试 ---")
	
	const messages = 100000
	
	// 无缓冲Channel
	start := time.Now()
	unbufferedCh := make(chan int)
	
	go func() {
		for i := 0; i < messages; i++ {
			unbufferedCh <- i
		}
		close(unbufferedCh)
	}()
	
	for range unbufferedCh {
		// 接收消息
	}
	
	unbufferedDuration := time.Since(start)
	fmt.Printf("无缓冲Channel %d 消息: %v (平均 %v/消息)\n", 
		messages, unbufferedDuration, unbufferedDuration/messages)
	
	// 有缓冲Channel
	start = time.Now()
	bufferedCh := make(chan int, 1000)
	
	go func() {
		for i := 0; i < messages; i++ {
			bufferedCh <- i
		}
		close(bufferedCh)
	}()
	
	for range bufferedCh {
		// 接收消息
	}
	
	bufferedDuration := time.Since(start)
	fmt.Printf("缓冲Channel %d 消息: %v (平均 %v/消息)\n", 
		messages, bufferedDuration, bufferedDuration/messages)
	
	fmt.Printf("缓冲Channel比无缓冲Channel快 %.2fx\n", 
		float64(unbufferedDuration)/float64(bufferedDuration))
}

// Mutex性能基准测试
func runMutexBenchmark() {
	fmt.Println("\n--- Mutex性能测试 ---")
	
	const operations = 1000000
	var mu sync.Mutex
	var rwmu sync.RWMutex
	var counter int64
	
	// Mutex写测试
	start := time.Now()
	var wg sync.WaitGroup
	
	for i := 0; i < 4; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < operations/4; j++ {
				mu.Lock()
				counter++
				mu.Unlock()
			}
		}()
	}
	
	wg.Wait()
	mutexDuration := time.Since(start)
	fmt.Printf("Mutex写操作 %d 次: %v (平均 %v/次)\n", 
		operations, mutexDuration, mutexDuration/operations)
	
	// RWMutex读测试
	counter = 0
	start = time.Now()
	
	for i := 0; i < 4; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < operations/4; j++ {
				rwmu.RLock()
				_ = counter
				rwmu.RUnlock()
			}
		}()
	}
	
	wg.Wait()
	rwmutexDuration := time.Since(start)
	fmt.Printf("RWMutex读操作 %d 次: %v (平均 %v/次)\n", 
		operations, rwmutexDuration, rwmutexDuration/operations)
	
	// 原子操作测试
	counter = 0
	start = time.Now()
	
	for i := 0; i < 4; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < operations/4; j++ {
				atomic.AddInt64(&counter, 1)
			}
		}()
	}
	
	wg.Wait()
	atomicDuration := time.Since(start)
	fmt.Printf("原子操作 %d 次: %v (平均 %v/次)\n", 
		operations, atomicDuration, atomicDuration/operations)
	
	fmt.Printf("性能对比 - Mutex: 1x, RWMutex读: %.2fx, 原子操作: %.2fx\n",
		float64(mutexDuration)/float64(rwmutexDuration),
		float64(mutexDuration)/float64(atomicDuration))
}

// 不同GOMAXPROCS设置的性能测试
func runDifferentGOMAXPROCSBenchmark() {
	fmt.Println("\n--- 不同GOMAXPROCS性能测试 ---")
	
	originalGOMAXPROCS := runtime.GOMAXPROCS(0)
	defer runtime.GOMAXPROCS(originalGOMAXPROCS)
	
	testCases := []int{1, 2, 4, 8, runtime.NumCPU()}
	
	for _, procs := range testCases {
		runtime.GOMAXPROCS(procs)
		
		// CPU密集型任务测试
		cpuDuration := benchmarkCPUIntensiveTask(procs)
		
		// I/O密集型任务测试
		ioDuration := benchmarkIOIntensiveTask(procs)
		
		// 混合任务测试
		mixedDuration := benchmarkMixedTask(procs)
		
		fmt.Printf("GOMAXPROCS=%d: CPU=%v, I/O=%v, 混合=%v\n", 
			procs, cpuDuration, ioDuration, mixedDuration)
	}
}

// CPU密集型任务基准测试
func benchmarkCPUIntensiveTask(procs int) time.Duration {
	const iterations = 10000000
	var wg sync.WaitGroup
	
	start := time.Now()
	
	for i := 0; i < procs*2; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			sum := 0
			for j := 0; j < iterations/procs/2; j++ {
				sum += j
			}
			_ = sum
		}()
	}
	
	wg.Wait()
	return time.Since(start)
}

// I/O密集型任务基准测试
func benchmarkIOIntensiveTask(procs int) time.Duration {
	var wg sync.WaitGroup
	
	start := time.Now()
	
	for i := 0; i < procs*4; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for j := 0; j < 10; j++ {
				time.Sleep(time.Millisecond)
			}
		}()
	}
	
	wg.Wait()
	return time.Since(start)
}

// 混合任务基准测试
func benchmarkMixedTask(procs int) time.Duration {
	var wg sync.WaitGroup
	
	start := time.Now()
	
	for i := 0; i < procs*2; i++ {
		wg.Add(1)
		go func(id int) {
			defer wg.Done()
			
			if id%2 == 0 {
				// CPU任务
				sum := 0
				for j := 0; j < 1000000; j++ {
					sum += j
				}
				_ = sum
			} else {
				// I/O任务
				time.Sleep(5 * time.Millisecond)
			}
		}(i)
	}
	
	wg.Wait()
	return time.Since(start)
}

// 可扩展性基准测试
func runScalabilityBenchmark() {
	fmt.Println("\n--- 可扩展性测试 ---")
	
	goroutineCounts := []int{10, 100, 1000, 10000}
	
	for _, count := range goroutineCounts {
		duration := benchmarkScalability(count)
		fmt.Printf("%d 个Goroutine: %v (平均 %v/个)\n", 
			count, duration, duration/time.Duration(count))
	}
}

// 可扩展性基准测试实现
func benchmarkScalability(goroutineCount int) time.Duration {
	var wg sync.WaitGroup
	ch := make(chan struct{}, goroutineCount)
	
	start := time.Now()
	
	for i := 0; i < goroutineCount; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			
			// 模拟一些工作
			for j := 0; j < 1000; j++ {
				select {
				case ch <- struct{}{}:
					<-ch
				default:
					runtime.Gosched()
				}
			}
		}()
	}
	
	wg.Wait()
	return time.Since(start)
}

// 基准测试函数（用于go test -bench）
func BenchmarkGoroutineCreation(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		var wg sync.WaitGroup
		wg.Add(1)
		go func() {
			defer wg.Done()
		}()
		wg.Wait()
	}
}

func BenchmarkChannelSend(b *testing.B) {
	ch := make(chan struct{}, 1)
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		ch <- struct{}{}
		<-ch
	}
}

func BenchmarkMutexLock(b *testing.B) {
	var mu sync.Mutex
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		mu.Lock()
		mu.Unlock()
	}
}

func BenchmarkAtomicAdd(b *testing.B) {
	var counter int64
	b.ResetTimer()
	
	for i := 0; i < b.N; i++ {
		atomic.AddInt64(&counter, 1)
	}
}

/*
基准测试使用说明：

1. 运行主程序：
   go run scheduler_benchmark.go

2. 运行基准测试：
   go test -bench=. scheduler_benchmark.go

3. 运行特定基准测试：
   go test -bench=BenchmarkGoroutineCreation scheduler_benchmark.go

4. 运行并发基准测试：
   go test -bench=. -cpu=1,2,4,8 scheduler_benchmark.go

5. 生成性能分析文件：
   go test -bench=. -cpuprofile=cpu.prof scheduler_benchmark.go
   go tool pprof cpu.prof

性能分析要点：

1. Goroutine创建开销：
   - 每个Goroutine约2KB初始栈空间
   - 创建成本相对较低，但大量创建仍有开销

2. 上下文切换开销：
   - Channel通信涉及调度器切换
   - runtime.Gosched()是最轻量的让出方式

3. 同步原语性能：
   - 原子操作 > RWMutex读 > Mutex > Channel
   - 选择合适的同步机制很重要

4. GOMAXPROCS影响：
   - CPU密集型：接近CPU核心数最优
   - I/O密集型：可以设置更大值
   - 混合负载：需要根据实际情况调优

5. 可扩展性：
   - Goroutine数量增加，单个开销可能增大
   - 调度器开销随Goroutine数量增长
*/
