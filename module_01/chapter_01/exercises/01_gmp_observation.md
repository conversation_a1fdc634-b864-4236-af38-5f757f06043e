# 练习1：观察GMP模型

## 练习目标

通过实际编程和观察，深入理解Go语言GMP模型的工作原理。

## 练习内容

### 任务1：基础观察

编写程序观察以下信息：
1. 系统CPU核心数
2. 当前GOMAXPROCS设置
3. 运行时Goroutine数量变化
4. 系统线程数量变化

**要求**：
- 创建不同数量的Goroutine
- 观察系统资源使用情况
- 记录关键数据的变化

### 任务2：P的数量实验

编写程序测试不同GOMAXPROCS设置对程序性能的影响：

```go
// 测试用例
testCases := []int{1, 2, 4, 8, runtime.NumCPU(), runtime.NumCPU()*2}
```

**测试场景**：
1. CPU密集型任务
2. I/O密集型任务
3. 混合型任务

**记录数据**：
- 执行时间
- CPU使用率
- 内存使用情况

### 任务3：M的创建观察

设计实验观察M（系统线程）的创建时机：

**实验设计**：
1. 创建大量阻塞的Goroutine
2. 创建大量CPU密集型Goroutine
3. 混合创建阻塞和非阻塞Goroutine

**观察要点**：
- 什么情况下会创建新的M？
- M的最大数量限制是多少？
- M如何被复用？

## 实现提示

### 获取运行时信息

```go
// 获取基本信息
fmt.Printf("CPU核心数: %d\n", runtime.NumCPU())
fmt.Printf("GOMAXPROCS: %d\n", runtime.GOMAXPROCS(0))
fmt.Printf("Goroutine数: %d\n", runtime.NumGoroutine())

// 获取内存统计
var m runtime.MemStats
runtime.ReadMemStats(&m)
fmt.Printf("堆内存: %d KB\n", m.Alloc/1024)
```

### 使用调度器跟踪

```bash
# 运行时启用调度器跟踪
GODEBUG=schedtrace=1000 go run your_program.go

# 输出解释：
# SCHED 1004ms: gomaxprocs=4 idleprocs=0 threads=5 spinningthreads=0 idlethreads=0 runqueue=0 [4 0 0 0]
# - gomaxprocs: P的数量
# - idleprocs: 空闲P数量  
# - threads: 总线程数(M)
# - runqueue: 全局队列G数量
# - [4 0 0 0]: 各P本地队列G数量
```

### 性能测试框架

```go
func benchmarkWithProcs(procs int, taskFunc func()) time.Duration {
    runtime.GOMAXPROCS(procs)
    
    start := time.Now()
    taskFunc()
    return time.Since(start)
}
```

## 预期结果

完成练习后，你应该能够：

1. **理解P的作用**：
   - P的数量如何影响并发性能
   - 最优P数量的选择策略
   - P与CPU核心数的关系

2. **观察M的行为**：
   - M的创建和销毁时机
   - 阻塞操作对M的影响
   - M的复用机制

3. **掌握G的调度**：
   - G在P之间的分配
   - G的状态转换
   - G的优先级机制

## 扩展思考

1. **为什么需要P这个中间层？**
   - 直接G到M的映射有什么问题？
   - P带来了哪些优势？

2. **GOMAXPROCS设置策略**：
   - 什么情况下应该设置为CPU核心数？
   - 什么情况下应该设置得更大或更小？

3. **调度器性能优化**：
   - 如何减少调度开销？
   - 如何避免调度器成为瓶颈？

## 提交要求

1. **代码实现**：
   - 完整的观察程序
   - 详细的注释说明
   - 测试用例覆盖

2. **实验报告**：
   - 实验数据记录
   - 现象分析和解释
   - 结论和心得体会

3. **性能对比**：
   - 不同配置下的性能数据
   - 图表展示（可选）
   - 最优配置建议

## 参考资料

- [Go Runtime Scheduler](https://golang.org/src/runtime/proc.go)
- [Goroutine调度器原理](https://draveness.me/golang/docs/part3-runtime/ch06-concurrency/golang-goroutine/)
- [Go调度器设计文档](https://docs.google.com/document/d/1TTj4T2JO42uD5ID9e89oa0sLKhJYD0Y_kqxDv3I3XMw)

## 评分标准

- **代码质量** (30%)：代码结构清晰，注释完整
- **实验设计** (25%)：实验设计合理，覆盖全面
- **数据分析** (25%)：数据记录准确，分析深入
- **理解深度** (20%)：对GMP模型理解正确，能够解释现象

---

**预计完成时间**：2-3小时  
**难度等级**：⭐⭐⭐  
**下一个练习**：[调度器行为分析](./02_scheduler_analysis.md)
