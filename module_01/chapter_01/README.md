# 第1章：Goroutine调度器原理

## 章节目标

深入理解Go语言的Goroutine调度器工作原理，掌握GMP模型、抢占式调度和工作窃取算法，为编写高效并发程序打下坚实基础。

## 核心概念

### 1. GMP模型
- **G (Goroutine)**：Go协程，用户级轻量线程
- **M (Machine)**：操作系统线程，执行G的载体
- **P (Processor)**：逻辑处理器，调度G到M的中介

### 2. 抢占式调度
- 协作式调度 vs 抢占式调度
- 基于信号的抢占机制
- 调度时机和触发条件

### 3. 工作窃取算法
- 负载均衡机制
- 本地队列和全局队列
- 窃取策略和性能优化

## 学习要点

1. **理解调度器架构**
   - GMP三者关系和交互
   - 调度器数据结构
   - 调度循环流程

2. **掌握调度策略**
   - 本地队列优先原则
   - 全局队列轮询机制
   - 网络轮询器集成

3. **性能优化技巧**
   - GOMAXPROCS设置
   - Goroutine池管理
   - 调度器监控

## 代码示例

### 基础示例
- [GMP模型演示](./examples/gmp_demo.go)
- [调度器行为观察](./examples/scheduler_behavior.go)
- [工作窃取演示](./examples/work_stealing.go)

### 高级示例
- [调度器性能测试](./examples/scheduler_benchmark.go)
- [Goroutine池实现](./examples/goroutine_pool.go)
- [调度器监控工具](./examples/scheduler_monitor.go)

## 练习题

### 基础练习
1. [观察GMP模型](./exercises/01_gmp_observation.md)
2. [调度器行为分析](./exercises/02_scheduler_analysis.md)
3. [工作窃取验证](./exercises/03_work_stealing_test.md)

### 进阶练习
4. [性能基准测试](./exercises/04_performance_benchmark.md)
5. [Goroutine池设计](./exercises/05_goroutine_pool_design.md)
6. [调度器调优](./exercises/06_scheduler_tuning.md)

## 重点难点

### 常见误区
- 认为Goroutine就是线程
- 忽略P的作用和重要性
- 不理解抢占式调度的时机

### 性能陷阱
- 创建过多Goroutine导致调度开销
- GOMAXPROCS设置不当
- 忽略调度器的公平性

### 调试技巧
- 使用GODEBUG=schedtrace查看调度信息
- 通过runtime包获取调度器状态
- 使用go tool trace分析调度行为

## 实战案例

### 案例1：CPU密集型任务调度优化
分析CPU密集型任务在不同GOMAXPROCS设置下的性能表现。

### 案例2：I/O密集型任务调度分析
观察I/O密集型任务如何与网络轮询器协作。

### 案例3：混合负载调度策略
设计处理混合负载的最优调度策略。

## 学习笔记模板

参考 [学习笔记模板](./notes/learning_notes_template.md) 记录学习过程中的重点和疑问。

## 参考资料

### 官方文档
- [Go Runtime Scheduler](https://golang.org/src/runtime/proc.go)
- [Go Scheduler Design](https://docs.google.com/document/d/1TTj4T2JO42uD5ID9e89oa0sLKhJYD0Y_kqxDv3I3XMw)

### 深度文章
- [Go's work-stealing scheduler](https://rakyll.org/scheduler/)
- [Goroutine调度器](https://draveness.me/golang/docs/part3-runtime/ch06-concurrency/golang-goroutine/)
- [Go调度器源码分析](https://www.ardanlabs.com/blog/2018/08/scheduling-in-go-part1.html)

### 相关书籍
- 《Go语言高级编程》第1章
- 《Go程序设计语言》第9章
- 《深入理解Go语言》调度器章节

## 检查点

完成本章学习后，请确认以下技能：

- [ ] 能够解释GMP模型的工作原理
- [ ] 理解抢占式调度的触发时机
- [ ] 掌握工作窃取算法的实现
- [ ] 能够分析调度器性能问题
- [ ] 会使用调度器相关的调试工具
- [ ] 能够优化Goroutine的使用

---

**预计学习时间**：3-4天  
**下一章**：[第2章：Channel高级模式](../chapter_02/README.md)
