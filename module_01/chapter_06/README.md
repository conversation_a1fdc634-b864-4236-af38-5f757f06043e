# 第6章：Context包应用

## 章节目标

深入掌握Go语言Context包的使用，包括请求链路控制、超时传播机制、取消信号处理，以及在实际项目中的最佳实践。

## 核心概念

### 1. Context基础
- Context接口设计
- 四种基本Context类型
- Context的生命周期
- 值传递和信号传递

### 2. 请求链路控制
- 请求追踪和传播
- 调用链管理
- 分布式追踪集成
- 元数据传递

### 3. 超时传播机制
- 超时控制的层次化
- 超时时间的计算
- 级联超时处理
- 优雅降级策略

### 4. 取消信号处理
- 主动取消和被动取消
- 取消信号的传播
- 资源清理机制
- 错误处理模式

## 学习要点

1. **理解Context的设计哲学**
   - 显式传递vs隐式传递
   - 不可变性和线程安全
   - 组合和继承关系
   - 性能考虑

2. **掌握Context的使用模式**
   - 函数签名设计
   - Context的创建和传递
   - 值的存储和获取
   - 取消和超时处理

3. **实际项目中的应用**
   - HTTP服务器集成
   - 数据库操作超时
   - gRPC调用控制
   - 微服务链路追踪

## 代码示例

### 基础示例
- [Context基础用法](./examples/context_basics.go)
- [超时控制模式](./examples/timeout_control.go)
- [取消信号处理](./examples/cancellation_handling.go)
- [值传递机制](./examples/value_passing.go)

### 高级示例
- [HTTP服务器集成](./examples/http_server_integration.go)
- [数据库操作超时](./examples/database_timeout.go)
- [gRPC调用控制](./examples/grpc_context.go)
- [分布式追踪](./examples/distributed_tracing.go)

## 练习题

### 基础练习
1. [Context基础操作](./exercises/01_context_basics.md)
2. [超时控制实现](./exercises/02_timeout_implementation.md)
3. [取消机制练习](./exercises/03_cancellation_practice.md)

### 进阶练习
4. [HTTP中间件设计](./exercises/04_http_middleware.md)
5. [分布式调用链](./exercises/05_distributed_call_chain.md)
6. [Context最佳实践](./exercises/06_context_best_practices.md)

## 重点难点

### Context的正确传递
- 函数参数位置
- 接口设计考虑
- 避免存储Context
- 生命周期管理

### 值传递的使用
- 何时使用WithValue
- 键的类型安全
- 避免滥用
- 性能影响

### 错误处理模式
- 超时错误的处理
- 取消错误的区分
- 错误信息的传递
- 优雅降级策略

## Context使用模式

### 1. 基本超时控制
```go
func doWork(ctx context.Context) error {
    ctx, cancel := context.WithTimeout(ctx, 5*time.Second)
    defer cancel()
    
    select {
    case <-time.After(10 * time.Second):
        return errors.New("work completed")
    case <-ctx.Done():
        return ctx.Err()
    }
}
```

### 2. 取消信号传播
```go
func worker(ctx context.Context, jobs <-chan Job) {
    for {
        select {
        case job := <-jobs:
            if err := processJob(ctx, job); err != nil {
                log.Printf("Job failed: %v", err)
            }
        case <-ctx.Done():
            log.Println("Worker shutting down")
            return
        }
    }
}
```

### 3. 值传递模式
```go
type contextKey string

const (
    userIDKey    contextKey = "userID"
    requestIDKey contextKey = "requestID"
)

func WithUserID(ctx context.Context, userID string) context.Context {
    return context.WithValue(ctx, userIDKey, userID)
}

func GetUserID(ctx context.Context) (string, bool) {
    userID, ok := ctx.Value(userIDKey).(string)
    return userID, ok
}
```

### 4. HTTP中间件集成
```go
func timeoutMiddleware(timeout time.Duration) func(http.Handler) http.Handler {
    return func(next http.Handler) http.Handler {
        return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
            ctx, cancel := context.WithTimeout(r.Context(), timeout)
            defer cancel()
            
            r = r.WithContext(ctx)
            next.ServeHTTP(w, r)
        })
    }
}
```

## 实战案例

### 案例1：HTTP API服务器
构建一个支持超时和取消的HTTP API服务器：
- 请求级别超时控制
- 优雅关闭机制
- 中间件链设计
- 错误处理和日志

### 案例2：数据库操作封装
实现支持Context的数据库操作层：
- 查询超时控制
- 事务管理
- 连接池集成
- 错误重试机制

### 案例3：微服务调用链
设计一个微服务调用链管理系统：
- 请求ID传播
- 调用链追踪
- 超时级联控制
- 性能监控集成

## Context最佳实践

### 1. 函数签名设计
```go
// 正确：Context作为第一个参数
func ProcessData(ctx context.Context, data []byte) error

// 错误：Context不是第一个参数
func ProcessData(data []byte, ctx context.Context) error

// 错误：将Context存储在结构体中
type Processor struct {
    ctx context.Context // 不推荐
}
```

### 2. Context创建和传递
```go
// 正确：从父Context派生
func handleRequest(w http.ResponseWriter, r *http.Request) {
    ctx := r.Context()
    ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
    defer cancel()
    
    processRequest(ctx, r)
}

// 错误：创建新的根Context
func handleRequest(w http.ResponseWriter, r *http.Request) {
    ctx := context.Background() // 丢失了请求Context
    processRequest(ctx, r)
}
```

### 3. 值传递的正确使用
```go
// 正确：使用类型安全的键
type contextKey string
const userIDKey contextKey = "userID"

// 错误：使用字符串作为键
ctx = context.WithValue(ctx, "userID", userID)
```

### 4. 错误处理
```go
func doWork(ctx context.Context) error {
    select {
    case <-time.After(workDuration):
        return nil
    case <-ctx.Done():
        switch ctx.Err() {
        case context.DeadlineExceeded:
            return errors.New("operation timed out")
        case context.Canceled:
            return errors.New("operation was canceled")
        default:
            return ctx.Err()
        }
    }
}
```

## 性能考虑

### Context开销
- Context创建的成本
- 值查找的性能
- 内存使用分析
- 优化策略

### 超时精度
- 定时器的精度限制
- 系统调用开销
- 批量超时处理
- 性能监控

## 与其他包的集成

### HTTP包集成
```go
// 获取请求Context
ctx := r.Context()

// 设置响应超时
ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
defer cancel()

// 传递给下游处理
result, err := processRequest(ctx, request)
```

### 数据库集成
```go
// database/sql包支持Context
rows, err := db.QueryContext(ctx, query, args...)
if err != nil {
    return err
}
defer rows.Close()
```

### gRPC集成
```go
// gRPC客户端调用
ctx, cancel := context.WithTimeout(context.Background(), time.Second)
defer cancel()

response, err := client.SomeMethod(ctx, request)
```

## 调试和监控

### Context状态监控
```go
func monitorContext(ctx context.Context) {
    go func() {
        <-ctx.Done()
        switch ctx.Err() {
        case context.DeadlineExceeded:
            log.Println("Context timed out")
        case context.Canceled:
            log.Println("Context was canceled")
        }
    }()
}
```

### 调用链追踪
```go
func traceContext(ctx context.Context, operation string) context.Context {
    span := trace.StartSpan(ctx, operation)
    return trace.NewContext(ctx, span)
}
```

## 参考资料

### 官方文档
- [context package](https://golang.org/pkg/context/)
- [Go Concurrency Patterns: Context](https://blog.golang.org/context)

### 深度文章
- [Understanding the context package](https://www.ardanlabs.com/blog/2019/09/context-package-semantics-in-go.html)
- [Context and Cancellation of goroutines](http://dahernan.github.io/2015/02/04/context-and-cancellation-of-goroutines/)

## 检查点

完成本章学习后，请确认以下技能：

- [ ] 理解Context的设计原理和使用场景
- [ ] 熟练使用各种Context创建函数
- [ ] 掌握超时和取消的处理模式
- [ ] 能够正确传递和使用Context值
- [ ] 具备Context在实际项目中的应用能力
- [ ] 理解Context的性能特性和最佳实践

---

**预计学习时间**：3-4天  
**前一章**：[第5章：并发安全设计](../chapter_05/README.md)  
**模块总结**：[模块1总结和项目实践](../README.md#实践项目)
